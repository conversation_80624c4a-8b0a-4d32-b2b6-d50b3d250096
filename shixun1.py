import torch
import torch.nn as nn
import torch.optim as optim

# 1. 创建简单数据： y = 2x + 1 的形式加上一点噪声
x = torch.tensor([[1.0], [2.0], [3.0], [4.0]], dtype=torch.float32)
y = torch.tensor([[3.0], [5.0], [7.0], [9.0]], dtype=torch.float32)  # y = 2x + 1

# 2. 定义最简单的单神经元网络（线性回归）
class SingleNeuronModel(nn.Module):
    def __init__(self):
        super(SingleNeuronModel, self).__init__()
        self.linear = nn.Linear(1, 1)  # 输入特征维度是1，输出也是1

    def forward(self, x):
        return self.linear(x)

# 实例化模型、损失函数和优化器
model = SingleNeuronModel()
criterion = nn.MSELoss()  # 均方误差损失
optimizer = optim.SGD(model.parameters(), lr=0.01)  # 随机梯度下降

# 3. 训练模型
epochs = 1000
for epoch in range(epochs):
    # Forward 前向传播
    outputs = model(x)
    loss = criterion(outputs, y)

    # Backward 反向传播和优化
    optimizer.zero_grad()
    loss.backward()
    optimizer.step()

    if (epoch + 1) % 100 == 0:
        print(f'Epoch [{epoch + 1}/{epochs}], Loss: {loss.item():.4f}')

# 4. 测试模型
model.eval()  # 设置为评估模式
with torch.no_grad():
    pred = model(torch.tensor([[5.0]]))
    print(f'输入预测值 5.0: {pred.item():.4f}')