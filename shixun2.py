import torch
import torch.nn as nn
import torch.optim as optim

# 构建非线性数据
X = torch.tensor([[1.0], [2.0], [3.0], [4.0]], dtype=torch.float32)
y = torch.tensor([[3.0], [10.0], [21.0], [36.0]], dtype=torch.float32)  # y = 2x^2 + x


# 定义浅层神经网络模型
class ShallowNet(nn.Module):
    def __init__(self):
        super(ShallowNet, self).__init__()
        self.hidden = nn.Linear(1, 10)  # 隐藏层
        self.relu = nn.ReLU()
        self.output = nn.Linear(10, 1)  # 输出层

    def forward(self, x):
        x = self.relu(self.hidden(x))
        return self.output(x)


# 初始化模型、损失函数和优化器
model = ShallowNet()
criterion = nn.MSELoss()
optimizer = optim.Adam(model.parameters(), lr=0.01)

# 训练模型
for epoch in range(1000):
    outputs = model(X)
    loss = criterion(outputs, y)
    optimizer.zero_grad()
    loss.backward()
    optimizer.step()

    if (epoch + 1) % 100 == 0:
        print(f'Epoch [{epoch + 1}/1000], Loss: {loss.item():.6f}')

# 测试预测
model.eval()
with torch.no_grad():
    test_input = torch.tensor([[5.0]])
    pred = model(test_input).item()
    print(f"预测值: {pred:.2f}, 真实值: {2 * (5 ** 2) + 5}")