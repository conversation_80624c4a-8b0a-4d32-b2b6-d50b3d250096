import torch
import torchvision
import torchvision.transforms as transforms
import matplotlib.pyplot as plt

# 类别标签
classes = ('plane', 'car', 'bird', 'cat', 'deer', 'dog', 'frog', 'horse', 'ship', 'truck')

# 设备配置
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")


# 定义网络结构
class Net(torch.nn.Module):
    def __init__(self):
        super(Net, self).__init__()
        self.conv1 = torch.nn.Conv2d(3, 6, 5)
        self.pool = torch.nn.MaxPool2d(2, 2)
        self.conv2 = torch.nn.Conv2d(6, 16, 5)
        self.fc1 = torch.nn.Linear(16 * 5 * 5, 120)
        self.fc2 = torch.nn.Linear(120, 84)
        self.fc3 = torch.nn.Linear(84, 10)

    def forward(self, x):
        x = self.pool(torch.relu(self.conv1(x)))
        x = self.pool(torch.relu(self.conv2(x)))
        x = x.view(-1, 16 * 5 * 5)
        x = torch.relu(self.fc1(x))
        x = torch.relu(self.fc2(x))
        x = self.fc3(x)
        return x


# 加载模型
net = Net()
checkpoint = torch.load('./checkpoint.pth')
net.load_state_dict(checkpoint['model_state_dict'])
net.to(device)
net.eval()

# 数据预处理
transform = transforms.Compose([
    transforms.ToTensor(),
    transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5))
])

# 加载测试集
testset = torchvision.datasets.CIFAR10(
    root='./data',
    train=False,
    download=True,
    transform=transform
)
testloader = torch.utils.data.DataLoader(testset, batch_size=4, shuffle=False)


# 图像显示函数
def imshow(img, label_idx):
    """显示图像及真实标签"""
    img = img.cpu().numpy().transpose((1, 2, 0))  # 转换为(H,W,C)格式
    img = img * 0.5 + 0.5  # 反归一化
    plt.imshow(img)
    plt.title(f'Label: {classes[label_idx.item()]}')  # 显示英文类别名
    plt.axis('off')
    plt.show()


if __name__ == '__main__':
    index = 3  # 选择要显示的图像索引

    # 获取一个批次的数据
    dataiter = iter(testloader)
    images, labels = next(dataiter)

    # 准备用于推理的单张图像
    image = images[index].unsqueeze(0).to(device)  # 添加批次维度并移至设备
    label = labels[index]

    # 显示图像
    imshow(images[index], label)

    # 执行推理
    with torch.no_grad():
        output = net(image)
        _, predicted = torch.max(output, 1)

    # 打印结果
    print(f'预测类别: {classes[predicted.item()]}')
    print(f'真实类别: {classes[label.item()]}')