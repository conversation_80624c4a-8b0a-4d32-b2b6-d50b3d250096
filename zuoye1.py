import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import matplotlib
matplotlib.use('TkAgg')  # 使用TkAgg后端，避免Qt问题
import matplotlib.pyplot as plt

# 设置随机种子确保结果可复现
torch.manual_seed(42)
np.random.seed(42)


# 定义目标函数 y = x^2 + 1
def target_function(x):
    return x ** 2 + 1


# 生成训练数据
def generate_data(n_samples=1000, x_range=(-5, 5)):
    x = np.random.uniform(x_range[0], x_range[1], n_samples)
    x = np.sort(x)  # 为了绘图方便排序
    y = target_function(x)

    # 添加一些噪声使问题更真实
    noise = np.random.normal(0, 1, n_samples)
    y_noisy = y + noise * 0.1

    # 转换为PyTorch张量
    x_tensor = torch.FloatTensor(x).view(-1, 1)
    y_tensor = torch.FloatTensor(y_noisy).view(-1, 1)

    return x_tensor, y_tensor


# 定义单神经元神经网络模型
class SingleNeuronNetwork(nn.Module):
    def __init__(self, hidden_size=10):
        super(SingleNeuronNetwork, self).__init__()
        # 输入层到隐藏层
        self.hidden = nn.Linear(1, hidden_size)
        # 隐藏层到输出层
        self.output = nn.Linear(hidden_size, 1)
        # 激活函数
        self.relu = nn.ReLU()

    def forward(self, x):
        x = self.relu(self.hidden(x))
        x = self.output(x)
        return x


# 训练函数
def train_model(model, x_train, y_train, epochs=1000, lr=0.01):
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=lr)

    losses = []

    for epoch in range(epochs):
        # 前向传播
        outputs = model(x_train)
        loss = criterion(outputs, y_train)

        # 反向传播和优化
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

        losses.append(loss.item())

        # 每100个epoch打印一次损失
        if (epoch + 1) % 100 == 0:
            print(f'Epoch [{epoch + 1}/{epochs}], Loss: {loss.item():.4f}')

    return losses


# 生成测试数据并评估模型
def evaluate_model(model, x_test, y_test):
    model.eval()
    with torch.no_grad():
        y_pred = model(x_test)
        mse = nn.MSELoss()(y_pred, y_test)
        print(f'Test MSE: {mse.item():.4f}')
    return y_pred


# 可视化结果
def visualize_results(x, y_true, y_pred, losses, save_file=True, show_plot=True):
    # 绘制损失曲线
    plt.figure(figsize=(12, 5))

    plt.subplot(1, 2, 1)
    plt.plot(losses)
    plt.title('Training Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')

    # 绘制函数拟合结果
    plt.subplot(1, 2, 2)
    plt.scatter(x.numpy(), y_true.numpy(), label='True Data', alpha=0.5)
    plt.plot(x.numpy(), y_pred.numpy(), 'r-', label='Predicted', linewidth=2)
    plt.title('Function Fitting: y = x^2 + 1')
    plt.xlabel('x')
    plt.ylabel('y')
    plt.legend()

    plt.tight_layout()

    # 保存图片
    if save_file:
        plt.savefig('neural_network_fitting.png', dpi=300, bbox_inches='tight')
        print("图形已保存为 'neural_network_fitting.png'")

    # 显示图片
    if show_plot:
        try:
            plt.show()
        except Exception as e:
            print(f"无法显示图形窗口: {e}")
            print("图形已保存为文件，请查看 'neural_network_fitting.png'")


# 主函数
def main():
    # 生成数据
    x_train, y_train = generate_data(n_samples=1000)

    # 创建模型
    model = SingleNeuronNetwork(hidden_size=20)

    # 训练模型
    losses = train_model(model, x_train, y_train, epochs=2000, lr=0.01)

    # 评估模型
    y_pred = evaluate_model(model, x_train, y_train)

    # 可视化结果
    visualize_results(x_train, y_train, y_pred, losses)

    # 保存模型
    torch.save(model.state_dict(), 'single_neuron_model.pth')
    print("模型已保存为 'single_neuron_model.pth'")


if __name__ == "__main__":
    main()
