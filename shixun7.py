import torch
import torchvision
import torchvision.transforms as transforms
import matplotlib.pyplot as plt
import numpy as np

# 设备配置
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# CIFAR-10 类别标签
classes = ('plane', 'car', 'bird', 'cat', 'deer', 'dog', 'frog', 'horse', 'ship', 'truck')


# 定义网络结构（必须与训练时一致）
class Net(torch.nn.Module):
    def __init__(self):
        super(Net, self).__init__()
        self.conv1 = torch.nn.Conv2d(3, 6, 5)
        self.pool = torch.nn.MaxPool2d(2, 2)
        self.conv2 = torch.nn.Conv2d(6, 16, 5)
        self.fc1 = torch.nn.Linear(16 * 5 * 5, 120)
        self.fc2 = torch.nn.Linear(120, 84)
        self.fc3 = torch.nn.Linear(84, 10)

    def forward(self, x):
        x = self.pool(torch.relu(self.conv1(x)))
        x = self.pool(torch.relu(self.conv2(x)))
        x = x.view(-1, 16 * 5 * 5)
        x = torch.relu(self.fc1(x))
        x = torch.relu(self.fc2(x))
        x = self.fc3(x)
        return x


# 加载模型权重
net = Net()
checkpoint = torch.load('./checkpoint.pth')
net.load_state_dict(checkpoint['model_state_dict'])
net.to(device)
net.eval()

# 数据预处理（必须与训练时一致）
transform = transforms.Compose([
    transforms.ToTensor(),
    transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5))
])

# 加载测试集
testset = torchvision.datasets.CIFAR10(
    root='./data',
    train=False,
    download=True,
    transform=transform
)
testloader = torch.utils.data.DataLoader(testset, batch_size=10, shuffle=False)


# 模型评估函数（计算准确率）
def evaluate_model(model, testloader, device):
    model.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for data in testloader:
            images, labels = data
            images, labels = images.to(device), labels.to(device)
            outputs = model(images)
            _, predicted = torch.max(outputs.data, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
    accuracy = 100 * correct / total
    print(f'测试集准确率: {accuracy:.2f}%')


# 图像显示函数
def imshow(img):
    img = img.cpu().numpy().transpose((1, 2, 0))
    img = img * 0.5 + 0.5  # 反归一化 [-1,1] -> [0,1]
    plt.imshow(img)
    plt.axis('off')
    plt.show()


# 可视化预测结果
def visualize_predictions(model, images, labels, classes, num_images=6):
    model.eval()
    images = images[:num_images].to(device)
    labels = labels[:num_images]

    with torch.no_grad():
        outputs = model(images)
        _, predicted = torch.max(outputs, 1)

    fig, axes = plt.subplots(1, num_images, figsize=(15, 5))
    for i in range(num_images):
        img = images[i].cpu().numpy().transpose((1, 2, 0))
        img = img * 0.5 + 0.5
        axes[i].imshow(img)
        axes[i].set_title(f'Pred: {classes[predicted[i]]}\nTrue: {classes[labels[i]]}', fontsize=10)
        axes[i].axis('off')

    plt.tight_layout()
    plt.show()


# 主程序入口
if __name__ == '__main__':
    # 模型评估
    evaluate_model(net, testloader, device)

    # 获取一个batch的数据
    dataiter = iter(testloader)
    images, labels = next(dataiter)

    # 显示前6张图像的预测结果和真实标签
    visualize_predictions(net, images, labels, classes, num_images=6)