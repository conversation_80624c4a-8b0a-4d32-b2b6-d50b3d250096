import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
from matplotlib.font_manager import FontProperties

# 设置随机种子，确保结果可复现
torch.manual_seed(42)
np.random.seed(42)


# 定义神经网络模型
class SimpleNet(nn.Module):
    def __init__(self):
        super(SimpleNet, self).__init__()
        # 一个隐藏层的神经网络，输入维度为1，隐藏层维度为10，输出维度为1
        self.hidden = nn.Linear(1, 10)
        self.relu = nn.ReLU()
        self.output = nn.Linear(10, 1)

    def forward(self, x):
        x = self.hidden(x)
        x = self.relu(x)
        x = self.output(x)
        return x


# 生成训练数据
def generate_data(n_samples=1000, x_range=(-5, 5)):
    x = np.random.uniform(x_range[0], x_range[1], n_samples)
    x = np.sort(x)  # 排序以便于绘图
    y = x ** 2 + 1 + np.random.normal(0, 0.1, n_samples)  # 添加一些噪声
    return torch.FloatTensor(x).view(-1, 1), torch.FloatTensor(y).view(-1, 1)


# 训练模型
def train_model(model, x_train, y_train, epochs=1000, lr=0.01):
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=lr)

    losses = []
    for epoch in range(epochs):
        # 前向传播
        y_pred = model(x_train)
        loss = criterion(y_pred, y_train)
        losses.append(loss.item())

        # 反向传播和优化
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

        # 每100个epoch打印一次损失
        if (epoch + 1) % 100 == 0:
            print(f'Epoch {epoch + 1}/{epochs}, Loss: {loss.item():.4f}')

    return losses


# 评估模型
def evaluate_model(model, x_test, y_test):
    model.eval()
    with torch.no_grad():
        y_pred = model(x_test)
        loss = nn.MSELoss()(y_pred, y_test)
        print(f'Test Loss: {loss.item():.4f}')
    return y_pred


# 可视化结果
def visualize_results(x, y_true, y_pred, losses):
    # 设置中文字体
    try:
        # 尝试使用系统中可能存在的中文字体
        font_path = "C:/Windows/Fonts/simhei.ttf"  # Windows系统的黑体字体
        font = FontProperties(fname=font_path)
    except:
        # 如果找不到指定字体，使用matplotlib默认字体
        font = FontProperties()

    plt.figure(figsize=(12, 5))

    # 绘制训练数据和模型预测
    plt.subplot(1, 2, 1)
    plt.scatter(x.numpy(), y_true.numpy(), label='真实数据', alpha=0.5)
    plt.plot(x.numpy(), y_pred.numpy(), 'r-', label='模型预测', linewidth=2)
    plt.legend(prop=font)
    plt.title('函数 y = x^2 + 1 的拟合结果', fontproperties=font)
    plt.xlabel('x', fontproperties=font)
    plt.ylabel('y', fontproperties=font)

    # 绘制损失曲线
    plt.subplot(1, 2, 2)
    plt.plot(losses)
    plt.title('训练损失曲线', fontproperties=font)
    plt.xlabel('Epoch', fontproperties=font)
    plt.ylabel('Loss', fontproperties=font)

    plt.tight_layout()
    plt.savefig('neural_network_results.png', dpi=300, bbox_inches='tight')
    print("图形已保存为 'neural_network_results.png'")


# 主函数
def main():
    # 生成数据
    x_train, y_train = generate_data(n_samples=1000)

    # 创建模型
    model = SimpleNet()

    # 训练模型
    losses = train_model(model, x_train, y_train, epochs=1000)

    # 评估模型
    y_pred = evaluate_model(model, x_train, y_train)

    # 可视化结果
    visualize_results(x_train, y_train, y_pred, losses)


if __name__ == '__main__':
    main()
