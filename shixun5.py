import matplotlib.pyplot as plt
import torch

# 加载checkpoint
checkpoint = torch.load('./checkpoint.pth')

# 获取loss和accuracy数据
losses = checkpoint['losses']
accuracies = checkpoint['accuracies']

# 绘制Loss曲线
plt.figure(figsize=(12, 5))

plt.subplot(1, 2, 1)
plt.plot(losses, label='Training Loss', marker='o')
plt.title('Training Loss Curve')
plt.xlabel('Every 2000 batches')
plt.ylabel('Loss')
plt.grid(True)
plt.legend()

# 绘制Accuracy曲线
plt.subplot(1, 2, 2)
plt.plot(accuracies, label='Training Accuracy', marker='o', color='r')
plt.title('Training Accuracy Curve')
plt.xlabel('Every 2000 batches')
plt.ylabel('Accuracy (%)')
plt.grid(True)
plt.legend()

plt.tight_layout()
plt.show()