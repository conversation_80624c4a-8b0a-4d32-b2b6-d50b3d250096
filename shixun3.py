import torch
import torchvision
import torchvision.transforms as transforms
import matplotlib.pyplot as plt
import numpy as np
from torchvision.utils import make_grid

# CIFAR-10 类别标签
classes = ('plane', 'car', 'bird', 'cat', 'deer', 'dog', 'frog', 'horse', 'ship', 'truck')

def imshow_single(img, label):
    """显示单张CIFAR图像及标签"""
    img = img.numpy().transpose((1, 2, 0))  # CHW -> HWC
    img = img * 0.5 + 0.5  # 反归一化 [-1,1] -> [0,1]
    plt.figure(figsize=(1, 1.5))  # 设置图像窗口大小为1x1.5英寸
    plt.imshow(img, interpolation='nearest')  # 禁用插值
    plt.title(f"Label: {label}", fontsize=8)
    plt.axis('off')
    plt.show()

def imshow_grid(img):
    """显示图像网格"""
    img = img.numpy().transpose((1, 2, 0))  # CHW -> HWC
    img = img * 0.5 + 0.5  # 反归一化 [-1,1] -> [0,1]
    plt.figure(figsize=(5, 2))  # 设置图像网格大小
    plt.imshow(img, interpolation='nearest')
    plt.axis('off')
    plt.show()

if __name__ == '__main__':
    # 数据预处理
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5))
    ])

    # 加载测试集
    testset = torchvision.datasets.CIFAR10(
        root='./data',
        train=False,
        download=True,
        transform=transform
    )
    testloader = torch.utils.data.DataLoader(
        testset,
        batch_size=10,
        shuffle=False,
        num_workers=2
    )

    # 获取一个batch的数据
    dataiter = iter(testloader)
    images, labels = next(dataiter)

    # 创建图像网格（每行5张）
    grid = make_grid(images, nrow=5, padding=2, pad_value=1)

    # 显示图像网格（无标签）
    imshow_grid(grid)

    # 单独显示每个图像和标签
    for i in range(len(images)):
        imshow_single(images[i], classes[labels[i]])