import torch
import torchvision
import torchvision.transforms as transforms
import matplotlib.pyplot as plt
import numpy as np
from torchvision.datasets import Flowers102

# 设置随机种子以确保结果可复现
torch.manual_seed(42)
np.random.seed(42)

# 定义图像变换
transform = transforms.Compose([
    transforms.Resize((224, 224)),  # 调整图像大小
    transforms.ToTensor(),          # 转换为张量
    transforms.Normalize(           # 标准化
        mean=[0.485, 0.456, 0.406],
        std=[0.229, 0.224, 0.225]
    )
])

# 加载Flowers102数据集
# root参数指定数据集下载位置，download=True表示如果数据集不存在则下载
# split='train'表示使用训练集
dataset = Flowers102(root='./data', split='train', download=True, transform=transform)

# 创建数据加载器
# batch_size=100表示每次加载100张图像
# shuffle=True表示打乱数据集
dataloader = torch.utils.data.DataLoader(dataset, batch_size=100, shuffle=True)

# 获取一批图像和标签
images, labels = next(iter(dataloader))

# 定义一个函数来反标准化图像以便显示
def imshow(img, title=None):
    img = img.numpy().transpose((1, 2, 0))  # 转换为numpy数组并调整维度顺序
    mean = np.array([0.485, 0.456, 0.406])
    std = np.array([0.229, 0.224, 0.225])
    img = std * img + mean  # 反标准化
    img = np.clip(img, 0, 1)  # 裁剪到[0,1]范围
    plt.imshow(img)
    if title is not None:
        plt.title(title)
    plt.axis('off')

# 显示10列图像（每行10张）
plt.figure(figsize=(20, 20))
for i in range(10):
    for j in range(10):
        idx = i * 10 + j
        if idx < len(images):
            plt.subplot(10, 10, idx + 1)
            imshow(images[idx], f"Label: {labels[idx].item()}")
plt.tight_layout()
plt.savefig('flowers_grid.png', dpi=300)
plt.show()

# 显示10张单独的图像
plt.figure(figsize=(15, 10))
for i in range(10):
    plt.subplot(2, 5, i + 1)
    imshow(images[i], f"Label: {labels[i].item()}")
plt.tight_layout()
plt.savefig('flowers_individual.png', dpi=300)
plt.show()

print("可视化完成！网格图像已保存为'flowers_grid.png'，单独图像已保存为'flowers_individual.png'")
