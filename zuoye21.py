import torch
import torchvision
import torchvision.transforms as transforms
import matplotlib.pyplot as plt
import numpy as np
from torchvision.datasets import Flowers102
from torch.multiprocessing import freeze_support
import matplotlib.font_manager as fm
import os


def main():
    # 尝试多种方法确保中文正常显示
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

    # 方法1: 尝试使用系统中常见的中文字体
    font_families = ["SimHei", "WenQuanYi Micro Hei", "Heiti TC", "Microsoft YaHei", "SimSun"]
    found_font = False

    for font in font_families:
        if font in [f.name for f in fm.fontManager.ttflist]:
            plt.rcParams["font.family"] = font
            print(f"找到并使用中文字体: {font}")
            found_font = True
            break

    # 方法2: 如果没有找到上述字体，尝试使用系统字体目录中的中文字体
    if not found_font:
        print("未找到常见中文字体，尝试从系统字体目录查找...")
        font_dirs = []

        # Windows系统字体目录
        if os.name == 'nt':
            font_dirs.append(os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'Fonts'))

        # Linux系统字体目录
        elif os.name == 'posix':
            font_dirs.append('/usr/share/fonts')
            font_dirs.append(os.path.expanduser('~/.fonts'))

        # macOS系统字体目录
        elif sys.platform == 'darwin':
            font_dirs.append('/System/Library/Fonts')
            font_dirs.append('/Library/Fonts')
            font_dirs.append(os.path.expanduser('~/Library/Fonts'))

        # 遍历字体目录，查找中文字体
        for font_dir in font_dirs:
            if os.path.exists(font_dir):
                for font_file in os.listdir(font_dir):
                    if font_file.endswith(('.ttf', '.ttc', '.otf')):
                        try:
                            font_path = os.path.join(font_dir, font_file)
                            font = fm.FontProperties(fname=font_path)
                            # 检查字体是否支持中文
                            if font.get_name() and any('\u4e00' <= c <= '\u9fff' for c in font.get_name()):
                                plt.rcParams["font.family"] = font.get_name()
                                print(f"找到并使用中文字体: {font.get_name()} 路径: {font_path}")
                                found_font = True
                                break
                        except:
                            continue
                    if found_font:
                        break
                if found_font:
                    break

    # 方法3: 如果仍未找到中文字体，使用备用方案
    if not found_font:
        print("未找到任何中文字体，使用备用方案...")
        try:
            # 尝试加载SimHei字体（Windows系统常用）
            font_path = "C:/Windows/Fonts/simhei.ttf"
            if os.path.exists(font_path):
                plt.rcParams["font.family"] = ["SimHei"]
                print(f"使用备用字体路径: {font_path}")
                found_font = True
        except:
            print("备用方案也失败了，将使用默认字体，中文可能无法正常显示")

    # 定义数据变换
    transform = transforms.Compose([
        transforms.Resize((224, 224)),  # 调整图像大小
        transforms.ToTensor(),  # 转换为张量
    ])

    # 加载Flowers102数据集
    dataset = Flowers102(
        root='./data',
        split='train',
        download=True,
        transform=transform
    )

    # 创建数据加载器
    dataloader = torch.utils.data.DataLoader(
        dataset,
        batch_size=10,
        shuffle=True,
        num_workers=2
    )

    # 获取类别名称
    class_names = dataset.classes

    # 从数据加载器中获取一批图像和标签
    images, labels = next(iter(dataloader))

    # 显示图像的函数
    def imshow(img, title):
        img = img / 2 + 0.5  # 反归一化
        npimg = img.numpy()
        plt.figure(figsize=(12, 12))
        plt.imshow(np.transpose(npimg, (1, 2, 0)))
        plt.title(title)
        plt.axis('off')
        plt.show()

    # 显示10张单独的图像
    for i in range(len(images)):
        imshow(images[i], f"类别: {class_names[labels[i]]}")

    # 显示10列图像
    plt.figure(figsize=(20, 20))
    for i in range(len(images)):
        plt.subplot(1, 10, i + 1)
        img = images[i] / 2 + 0.5  # 反归一化
        npimg = img.numpy()
        plt.imshow(np.transpose(npimg, (1, 2, 0)))
        plt.title(f"{class_names[labels[i]]}", fontsize=8)
        plt.axis('off')

    plt.tight_layout()
    plt.show()


if __name__ == '__main__':
    freeze_support()
    main()
